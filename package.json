{"name": "waste-marketplace", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-dropdown-menu": "^2.1.12", "@radix-ui/react-popover": "^1.1.11", "@radix-ui/react-slot": "^1.2.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "emailjs-com": "^3.2.0", "embla-carousel-autoplay": "^8.6.0", "embla-carousel-react": "^8.6.0", "framer-motion": "^12.9.4", "lucide-react": "^0.507.0", "next": "15.3.0", "react": "^19.0.0", "react-dom": "^19.0.0", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4.1.11", "@tailwindcss/typography": "^0.5.16", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.3.0", "tailwindcss": "^4.1.11", "tw-animate-css": "^1.2.9", "typescript": "^5"}}