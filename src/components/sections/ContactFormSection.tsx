"use client";

import React, { useState, useEffect } from 'react';
import emailjs from 'emailjs-com';
import logger from '@/utils/logger';

const ContactFormSection = () => {
  // Initialize EmailJS with key from environment variable
  useEffect(() => {
    emailjs.init(process.env.NEXT_PUBLIC_EMAILJS_CONTACT_PUBLIC_KEY || 'GwpR6HErNwXDhHY8_');
  }, []);
  // Form state
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: ''
  });

  // Loading and success/error states
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<{ success: boolean; message: string } | null>(null);

  // Handle input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prevData => ({
      ...prevData,
      [name]: value
    }));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setSubmitStatus(null);

    try {
      // Send email using EmailJS
      await emailjs.send(
        process.env.NEXT_PUBLIC_EMAILJS_SERVICE_ID_CONTACT || 'service_3xw1ha8', // EmailJS service ID
        process.env.NEXT_PUBLIC_EMAILJS_TEMPLATE_ID_CONTACT || 'template_8ofcv4m', // EmailJS template ID
        {
          name: formData.name,
          email: formData.email,
          subject: formData.subject,
          message: formData.message,
          time: new Date().toLocaleString()
        },
        process.env.NEXT_PUBLIC_EMAILJS_CONTACT_PUBLIC_KEY || 'GwpR6HErNwXDhHY8_' // EmailJS public key
      );

      // Handle success
      setSubmitStatus({
        success: true,
        message: 'Your message has been sent successfully!'
      });

      // Reset form
      setFormData({
        name: '',
        email: '',
        subject: '',
        message: ''
      });
    } catch (error) {
      // Handle error
      logger.error('Error sending email:', error);
      setSubmitStatus({
        success: false,
        message: 'Failed to send message. Please try again later.'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <section id="contact-section" className="py-12 md:py-16 bg-gray-50 min-h-[600px] flex items-center">
      <div className="mx-auto px-4 md:px-6 lg:px-8 w-full max-w-7xl">
        <div className="flex flex-col md:flex-row gap-8 items-center">
          {/* Left side - Contact us text */}
          <div className="md:w-1/2 flex items-center self-stretch">
            <div>
              <h3 className="text-lg font-medium text-[#FF8A00] mb-4">Contact us</h3>

              <h2 className="text-2xl md:text-3xl font-semibold text-[#1E2A36] mb-4">
                Couldn&apos;t find your answer?<br/> Let&apos;s start a conversation
              </h2>

              <p className="text-sm md:text-base text-gray-600 mb-4">
                Our team is ready to answer your specific questions and help you<br/> discover how Nordic Loop can transform your waste<br/> management approach and reduce your environmental impact.
              </p>
            </div>
          </div>

          {/* Right side - Contact form */}
          <div className="md:w-1/2 bg-white p-6 rounded-lg shadow-md self-stretch">
            <h3 className="text-lg font-medium text-[#FF8A00] mb-4">Send Us Your Question</h3>

            {/* Status message */}
            {submitStatus && (
              <div className={`p-3 mb-4 rounded-md ${submitStatus.success ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'}`}>
                {submitStatus.message}
              </div>
            )}

            <form className="space-y-4" onSubmit={handleSubmit}>
              {/* Name input */}
              <div>
                <label htmlFor="name" className="block text-sm text-gray-600 mb-1">Name</label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  placeholder="Enter your name"
                  className="w-full p-3 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-[#FF8A00] text-sm"
                  required
                />
              </div>

              {/* Email input */}
              <div>
                <label htmlFor="email" className="block text-sm text-gray-600 mb-1">Email</label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={handleChange}
                  placeholder="Enter your email"
                  className="w-full p-3 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-[#FF8A00] text-sm"
                  required
                />
              </div>

              {/* Subject input */}
              <div>
                <label htmlFor="subject" className="block text-sm text-gray-600 mb-1">Subject</label>
                <input
                  type="text"
                  id="subject"
                  name="subject"
                  value={formData.subject}
                  onChange={handleChange}
                  placeholder="Enter subject"
                  className="w-full p-3 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-[#FF8A00] text-sm"
                  required
                />
              </div>

              {/* Message input */}
              <div>
                <label htmlFor="message" className="block text-sm text-gray-600 mb-1">Message</label>
                <textarea
                  id="message"
                  name="message"
                  value={formData.message}
                  onChange={handleChange}
                  placeholder="Enter your message"
                  rows={4}
                  className="w-full p-3 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-[#FF8A00] text-sm"
                  required
                />
              </div>

              {/* Submit button */}
              <button
                type="submit"
                className="w-full bg-[#FF8A00] text-white py-3 px-4 rounded-md hover:bg-[#e67e00] transition-colors text-center font-medium"
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Sending...' : 'Send A Message'}
              </button>
            </form>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ContactFormSection;